<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E293B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="k8sGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7C3AED;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5B21B6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <circle cx="256" cy="256" r="256" fill="url(#bgGradient)"/>
  
  <!-- Cloud shape -->
  <path d="M128 288C128 235.008 171.008 192 224 192H288C340.992 192 384 235.008 384 288C384 340.992 340.992 384 288 384H224C171.008 384 128 340.992 128 288Z" fill="url(#cloudGradient)"/>
  
  <!-- Kubernetes hexagon -->
  <path d="M256 96L328 144V240L256 288L184 240V144L256 96Z" fill="url(#k8sGradient)"/>
  
  <!-- K8s inner elements -->
  <circle cx="256" cy="192" r="24" fill="white"/>
  <circle cx="208" cy="240" r="24" fill="white"/>
  <circle cx="304" cy="240" r="24" fill="white"/>
  
  <!-- Connection lines -->
  <path d="M256 216L208 240" stroke="white" stroke-width="8" opacity="0.8"/>
  <path d="M256 216L304 240" stroke="white" stroke-width="8" opacity="0.8"/>
  <path d="M208 240L304 240" stroke="white" stroke-width="8" opacity="0.6"/>
</svg> 