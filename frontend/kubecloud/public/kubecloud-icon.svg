<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E293B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cloudGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="k8sGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7C3AED;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5B21B6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <circle cx="16" cy="16" r="16" fill="url(#bgGradient)"/>
  
  <!-- Cloud shape -->
  <path d="M8 18C8 14.6863 10.6863 12 14 12H18C21.3137 12 24 14.6863 24 18C24 21.3137 21.3137 24 18 24H14C10.6863 24 8 21.3137 8 18Z" fill="url(#cloudGradient)"/>
  
  <!-- Kubernetes hexagon -->
  <path d="M16 6L20.5 9V15L16 18L11.5 15V9L16 6Z" fill="url(#k8sGradient)"/>
  
  <!-- K8s inner elements -->
  <circle cx="16" cy="12" r="1.5" fill="white"/>
  <circle cx="13" cy="15" r="1.5" fill="white"/>
  <circle cx="19" cy="15" r="1.5" fill="white"/>
  
  <!-- Connection lines -->
  <path d="M16 13.5L13 15" stroke="white" stroke-width="0.5" opacity="0.8"/>
  <path d="M16 13.5L19 15" stroke="white" stroke-width="0.5" opacity="0.8"/>
  <path d="M13 15L19 15" stroke="white" stroke-width="0.5" opacity="0.6"/>
</svg> 