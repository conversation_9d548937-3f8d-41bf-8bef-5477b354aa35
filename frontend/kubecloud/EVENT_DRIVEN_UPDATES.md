# Event-Driven Updates System

This document explains the new event-driven update system that replaces the previous polling mechanism for cluster data updates.

## Overview

The new system uses Server-Sent Events (SSE) to provide real-time updates when cluster operations complete, eliminating the need for constant polling and providing a better user experience.

## How It Works

### Backend Events

The backend sends specific events when cluster operations complete:

1. **`deployment_update`** - When a cluster deployment completes or fails
2. **`node_added`** - When a node is successfully added to a cluster
3. **`node_removed`** - When a node is successfully removed from a cluster
4. **`cluster_deleted`** - When a cluster is successfully deleted
5. **`workflow_update`** - General workflow status updates

### Frontend Event Handling

The `useDeploymentEvents` composable handles these events and automatically refreshes the relevant data:

```typescript
// Event handlers automatically refresh data when operations complete
function handleWorkflowUpdate(event: ClusterEvent) {
  const { data } = event
  const status = data.status || data.data?.status
  
  if (status === 'completed' || status === 'failed') {
    // Refresh cluster data when workflow completes
    refreshClusterData()
    
    if (status === 'completed') {
      notificationStore.success('Cluster Operation', 'Operation completed successfully')
    } else {
      notificationStore.error('Cluster Operation Failed', 'Operation failed')
    }
  }
}
```

## Benefits

### Before (Polling)
- ❌ Constant API calls every 10-15 seconds
- ❌ Visual "refreshing" effect that was jarring
- ❌ Unnecessary server load
- ❌ Delayed updates (up to 15 seconds)

### After (Event-Driven)
- ✅ Real-time updates when operations complete
- ✅ No visual "refreshing" effect
- ✅ Reduced server load
- ✅ Immediate updates
- ✅ Better user experience

## Implementation

### Global Event System

The event system is initialized globally in `main.ts`:

```typescript
// Initialize deployment events system globally
const { connect: connectDeploymentEvents } = useDeploymentEvents()
if (userStore.token) {
  connectDeploymentEvents()
}
```

### Component Usage

Components no longer need to implement polling. They simply use the cluster store, and data is automatically refreshed when events are received:

```typescript
// Before: Polling every 10 seconds
const pollInterval = setInterval(pollClusterData, 10000)

// After: No polling needed - events handle updates automatically
const { isConnected } = useDeploymentEvents()
```

### Event Types

The system handles these specific event types:

- **`workflow_update`** - General workflow status updates
- **`deployment_update`** - Cluster deployment status
- **`cluster_updated`** - Cluster information updates
- **`node_added`** - Node addition completion
- **`node_removed`** - Node removal completion
- **`cluster_deleted`** - Cluster deletion completion

## Migration Guide

### For Components

1. Remove polling intervals and cleanup code
2. Remove manual refresh calls after operations
3. The event system automatically handles data updates

### For Backend

1. Events are automatically sent when workflows complete
2. No additional backend changes needed
3. Events include proper status and error information

## Error Handling

The system includes robust error handling:

- Automatic reconnection on connection loss
- Duplicate event prevention
- Graceful fallback for unknown event types
- Connection status monitoring

## Performance Impact

- **Reduced API calls**: From ~240 calls/hour (15s polling) to ~0 calls/hour
- **Faster updates**: Real-time vs 15-second delay
- **Better UX**: No visual "refreshing" effect
- **Lower server load**: Only updates when needed