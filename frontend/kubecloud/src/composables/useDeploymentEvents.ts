import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useNotificationStore } from '../stores/notifications'
import { useUserStore } from '../stores/user'
import { useClusterStore } from '../stores/clusters'
import { useNodeManagement } from './useNodeManagement'

export interface DeploymentEvent {
  type: string
  data: any
  task_id?: string
  timestamp: string
}

export function useDeploymentEvents() {
  const eventSource = ref<EventSource | null>(null)
  const notificationStore = useNotificationStore()
  const userStore = useUserStore()
  const clusterStore = useClusterStore()
  const { fetchRentedNodes } = useNodeManagement()

  const seenTaskIds = new Set<string>()
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 2000 // 2 seconds

  // Event handlers for different operations
  const eventHandlers = {
    'workflow_update': handleWorkflowUpdate,
    'deployment_update': handleDeploymentUpdate,
    'cluster_updated': handleClusterUpdated,
    'node_added': handleNodeAdded,
    'node_removed': handleNodeRemoved,
    'cluster_deleted': handleClusterDeleted
  }

  function connect() {
    if (eventSource.value) return

    const backendBaseUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'
    const token = userStore.token || ''
    const url = backendBaseUrl + '/api/v1/events?token=' + encodeURIComponent(token)

    eventSource.value = new EventSource(url, { withCredentials: true })

    eventSource.value.onopen = () => {
      isConnected.value = true
      reconnectAttempts.value = 0
      console.log('SSE connection established')
    }

    eventSource.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data) as DeploymentEvent
        const type = data.type || 'info'

        if (type === 'connected') {
          isConnected.value = true
          return
        }

        // Prevent duplicate notifications
        const taskId = data.task_id || data.data?.task_id
        if (taskId && seenTaskIds.has(taskId)) return
        if (taskId) seenTaskIds.add(taskId)

        // Handle specific event types
        const handler = eventHandlers[type as keyof typeof eventHandlers]
        if (handler) {
          handler(data)
        } else {
          // Fallback for unknown event types
          handleGenericEvent(data)
        }
      } catch (err) {
        console.error('Error parsing SSE message:', err)
        handleGenericEvent({ type: 'error', data: event.data, timestamp: new Date().toISOString() })
      }
    }

    eventSource.value.onerror = (err) => {
      isConnected.value = false
      console.error('SSE connection error:', err)

      // Attempt to reconnect
      if (reconnectAttempts.value < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.value++
          disconnect()
          connect()
        }, reconnectDelay * reconnectAttempts.value)
      } else {
        console.error('Max reconnection attempts reached')
      }
    }
  }

  function disconnect() {
    if (eventSource.value) {
      eventSource.value.close()
      eventSource.value = null
    }
    isConnected.value = false
  }

  // Event handlers
  function handleWorkflowUpdate(event: DeploymentEvent) {
    const { data } = event
    const status = data.status || data.data?.status
    const message = data.message || data.data?.message
    const error = data.error || data.data?.error

    if (status === 'completed' || status === 'failed') {
      // Refresh cluster data when workflow completes
      refreshClusterData()

      if (status === 'completed') {
        notificationStore.success('Cluster Operation', message || 'Operation completed successfully')
      } else {
        notificationStore.error('Cluster Operation Failed', error || message || 'Operation failed')
      }
    } else {
      notificationStore.info('Cluster Operation', message || 'Operation in progress')
    }
  }

  function handleDeploymentUpdate(event: DeploymentEvent) {
    const { data } = event
    const status = data.status || data.data?.status
    const message = data.message || data.data?.message

    if (status === 'completed' || status === 'failed') {
      // Refresh cluster data when deployment completes
      refreshClusterData()

      if (status === 'completed') {
        notificationStore.success('Deployment', message || 'Deployment completed successfully')
      } else {
        notificationStore.error('Deployment Failed', message || 'Deployment failed')
      }
    } else {
      notificationStore.info('Deployment', message || 'Deployment in progress')
    }
  }

  function handleClusterUpdated(event: DeploymentEvent) {
    refreshClusterData()
    notificationStore.info('Cluster Updated', 'Cluster information has been updated')
  }

  function handleNodeAdded(event: DeploymentEvent) {
    refreshClusterData()
    notificationStore.success('Node Added', 'Node has been successfully added to the cluster')
  }

  function handleNodeRemoved(event: DeploymentEvent) {
    refreshClusterData()
    notificationStore.success('Node Removed', 'Node has been successfully removed from the cluster')
  }

  function handleClusterDeleted(event: DeploymentEvent) {
    refreshClusterData()
    notificationStore.success('Cluster Deleted', 'Cluster has been successfully deleted')
  }

  function handleGenericEvent(event: DeploymentEvent) {
    const message = event.data?.message || JSON.stringify(event.data)
    const type = event.type || 'info'

    if (type === 'success') {
      notificationStore.success('Deployment', message)
    } else if (type === 'error') {
      notificationStore.error('Deployment Error', message)
    } else {
      notificationStore.info('Deployment', message)
    }
  }

  // Refresh all cluster-related data
  async function refreshClusterData() {
    try {
      await Promise.all([
        clusterStore.fetchClusters(),
        fetchRentedNodes()
      ])
    } catch (error) {
      console.error('Error refreshing cluster data:', error)
    }
  }

  // Manual refresh function for components that need it
  function manualRefresh() {
    return refreshClusterData()
  }

  // Watch for token changes to reconnect
  watch(() => userStore.token, (newToken) => {
    if (newToken && !isConnected.value) {
      connect()
    } else if (!newToken) {
      disconnect()
    }
  })

  onMounted(() => {
    if (userStore.token) {
      connect()
    }
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    connect,
    disconnect,
    manualRefresh,
    isConnected: computed(() => isConnected.value),
    refreshClusterData
  }
}
