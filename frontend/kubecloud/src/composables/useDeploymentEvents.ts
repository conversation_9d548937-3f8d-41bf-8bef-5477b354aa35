import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useNotificationStore } from '../stores/notifications'
import { useUserStore } from '../stores/user'
import { useClusterStore } from '../stores/clusters'
import { useNodeManagement } from './useNodeManagement'

export interface DeploymentEvent {
  type: string
  data: any
  message?: string
  timestamp: string
}

export function useDeploymentEvents() {
  const eventSource = ref<EventSource | null>(null)
  const notificationStore = useNotificationStore()
  const userStore = useUserStore()
  const clusterStore = useClusterStore()
  const { fetchRentedNodes } = useNodeManagement()

  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 2000 // 2 seconds
  const connectionCheckInterval = ref<ReturnType<typeof setInterval> | null>(null)

  function connect() {
    if (eventSource.value) return

    const backendBaseUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'
    const token = userStore.token || ''
    const url = backendBaseUrl + '/api/v1/events?token=' + encodeURIComponent(token)

    eventSource.value = new EventSource(url, { withCredentials: true })

    eventSource.value.onopen = () => {
      isConnected.value = true
      reconnectAttempts.value = 0
      console.log('SSE connection established')

      // Start periodic connection check
      startConnectionCheck()
    }

    eventSource.value.onmessage = (event) => {
        const data = JSON.parse(event.data) as DeploymentEvent
        const type = data.type || 'info'

        if (type === 'connected') {
          isConnected.value = true
          return
        }

        // Handle workflow updates
        if (type === 'workflow_update') {
          const message = data.message || data.data?.message
          if (message) {
            // Check if workflow failed based on message content
            if (message.toLowerCase().includes('failed')) {
              notificationStore.error('Workflow', message)
            } else if (message.toLowerCase().includes('completed')) {
              notificationStore.success('Workflow', message)
            }
          }

          // Always refresh data when workflow completes (success or failure)
          refreshClusterData()
        }
    }

    eventSource.value.onerror = (err) => {
      isConnected.value = false
      console.error('SSE connection error:', err)

      // Attempt to reconnect
      if (reconnectAttempts.value < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.value++
          disconnect()
          connect()
        }, reconnectDelay * reconnectAttempts.value)
      }
    }
  }

  function disconnect() {
    console.log('Disconnecting SSE...')

    // Clear connection check interval
    if (connectionCheckInterval.value) {
      clearInterval(connectionCheckInterval.value)
      connectionCheckInterval.value = null
    }

    if (eventSource.value) {
      eventSource.value.close()
      eventSource.value = null
    }
    isConnected.value = false
  }

  // Start periodic connection check to ensure connection stays alive
  function startConnectionCheck() {
    // Clear any existing interval
    if (connectionCheckInterval.value) {
      clearInterval(connectionCheckInterval.value)
    }

    // Check connection every 30 seconds
    connectionCheckInterval.value = setInterval(() => {
      if (userStore.token && !isConnected.value) {
        console.log('Connection check: not connected but should be, reconnecting...')
        connect()
      } else if (!userStore.token && isConnected.value) {
        console.log('Connection check: connected but no token, disconnecting...')
        disconnect()
      }
    }, 30000) // 30 seconds
  }

  // Refresh all cluster-related data
  async function refreshClusterData() {
    await Promise.all([
      clusterStore.fetchClusters(),
      fetchRentedNodes()
    ])
  }

  // Manual refresh function for components that need it
  function manualRefresh() {
    return refreshClusterData()
  }

  // Watch for token changes to reconnect
  watch(() => userStore.token, (newToken, oldToken) => {
    console.log('Token changed:', { newToken: !!newToken, oldToken: !!oldToken, isConnected: isConnected.value })

    if (newToken && !isConnected.value) {
      console.log('Token available and not connected, connecting...')
      connect()
    } else if (!newToken && isConnected.value) {
      console.log('Token removed, disconnecting...')
      disconnect()
    }
  }, { immediate: true }) // immediate: true ensures it runs on mount

  onMounted(async () => {
    console.log('useDeploymentEvents mounted')

    // Wait for user store to initialize if needed
    if (!userStore.token && userStore.isLoggedIn === undefined) {
      console.log('Waiting for user store to initialize...')
      // Wait a bit for the user store to load from localStorage
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    if (userStore.token && !isConnected.value) {
      console.log('Token available on mount, connecting...')
      connect()
    }
  })

  onUnmounted(() => {
    disconnect()
    // Clean up event listeners
    if (typeof window !== 'undefined') {
      window.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('online', handleOnline)
    }
  })

  // Handle page visibility changes (user switches tabs/windows)
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible' && userStore.token && !isConnected.value) {
      console.log('Page became visible, checking connection...')
      connect()
    }
  }

  // Handle network reconnection
  const handleOnline = () => {
    if (userStore.token && !isConnected.value) {
      console.log('Network came back online, reconnecting...')
      connect()
    }
  }

  // Add event listeners for page visibility and network status
  if (typeof window !== 'undefined') {
    window.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('online', handleOnline)
  }

  return {
    connect,
    disconnect,
    manualRefresh,
    refreshClusterData,
    isConnected: computed(() => isConnected.value)
  }
}
