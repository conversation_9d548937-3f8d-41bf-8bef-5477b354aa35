import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useNotificationStore } from '../stores/notifications'
import { useUserStore } from '../stores/user'
import { useClusterStore } from '../stores/clusters'
import { useNodeManagement } from './useNodeManagement'

export interface DeploymentEvent {
  type: string
  data: any
  message?: string
  task_id?: string
  timestamp: string
}

export function useDeploymentEvents() {
  const eventSource = ref<EventSource | null>(null)
  const notificationStore = useNotificationStore()
  const userStore = useUserStore()
  const clusterStore = useClusterStore()
  const { fetchRentedNodes } = useNodeManagement()

  const seenTaskIds = new Set<string>()
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 2000 // 2 seconds

  function connect() {
    if (eventSource.value) return

    const backendBaseUrl = import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080'
    const token = userStore.token || ''
    const url = backendBaseUrl + '/api/v1/events?token=' + encodeURIComponent(token)

    eventSource.value = new EventSource(url, { withCredentials: true })

    eventSource.value.onopen = () => {
      isConnected.value = true
      reconnectAttempts.value = 0
      console.log('SSE connection established')
    }

    eventSource.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data) as DeploymentEvent
        const type = data.type || 'info'

        if (type === 'connected') {
          isConnected.value = true
          return
        }

        // Prevent duplicate processing
        const taskId = data.task_id || data.data?.task_id
        if (taskId && seenTaskIds.has(taskId)) return
        if (taskId) seenTaskIds.add(taskId)

        // Show the workflow message as notification (backend already sends the message)
        const message = data.data?.message || data.message
        if (message) {
          // Check if workflow failed based on message content
          if (message.toLowerCase().includes('failed') || message.toLowerCase().includes('error')) {
            notificationStore.error('Workflow', message)
          } else {
            notificationStore.success('Workflow', message)
          }
        }

        // Refresh data for any workflow completion
        if (type === 'workflow_update' || type === 'deployment_update' ||
            type === 'cluster_updated' || type === 'node_added' ||
            type === 'node_removed' || type === 'cluster_deleted') {
          refreshClusterData()
        }
      } catch (err) {
        console.error('Error parsing SSE message:', err)
      }
    }

    eventSource.value.onerror = (err) => {
      isConnected.value = false
      console.error('SSE connection error:', err)

      // Attempt to reconnect
      if (reconnectAttempts.value < maxReconnectAttempts) {
        setTimeout(() => {
          reconnectAttempts.value++
          disconnect()
          connect()
        }, reconnectDelay * reconnectAttempts.value)
      } else {
        console.error('Max reconnection attempts reached')
      }
    }
  }

  function disconnect() {
    if (eventSource.value) {
      eventSource.value.close()
      eventSource.value = null
    }
    isConnected.value = false
  }

  // Refresh all cluster-related data
  async function refreshClusterData() {
    try {
      await Promise.all([
        clusterStore.fetchClusters(),
        fetchRentedNodes()
      ])
    } catch (error) {
      console.error('Error refreshing cluster data:', error)
    }
  }

  // Manual refresh function for components that need it
  function manualRefresh() {
    return refreshClusterData()
  }

  // Watch for token changes to reconnect
  watch(() => userStore.token, (newToken) => {
    if (newToken && !isConnected.value) {
      connect()
    } else if (!newToken) {
      disconnect()
    }
  })

  onMounted(() => {
    if (userStore.token) {
      connect()
    }
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    connect,
    disconnect,
    manualRefresh,
    isConnected: computed(() => isConnected.value),
    refreshClusterData
  }
}
