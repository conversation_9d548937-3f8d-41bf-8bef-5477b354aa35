version: "3"
services:
  redis-db:
    image: redis:alpine
    command: redis-server --requirepass pass
    ports:
      - 6379:6379
    container_name: redis-db

  backend:
    build: ./backend
    ports:
      - "8080:8080"
    depends_on:
      - redis-db
    working_dir: /opt/project      
    volumes:
      - ./backend/kubecloud.db:/app/kubecloud.db
      - ./backend/config.json:/app/config.json
      - /root/.ssh:/root/.ssh:ro
    command: ["/kubecloud", "--config", "/app/config.json"]

  frontend:
    environment:
      - VITE_API_BASE_URL=http://localhost:8080/api
      - VITE_STRIPE_PUBLISHABLE_KEY=""
    build:
      context: frontend/kubecloud/.
      dockerfile: Dockerfile
    ports:
       - "8000:80"
